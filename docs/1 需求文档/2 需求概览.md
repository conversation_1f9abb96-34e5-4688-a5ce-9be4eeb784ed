
---

### **喵语战记**  
**需求文档**  

---

### **一、角色系统**  
#### **1. 人类主角**  
| **属性**         | **规则说明**                                                                 |
|------------------|-----------------------------------------------------------------------------|
| 角色定制         | 可选男孩/女孩形象，支持更换服装配饰                                         |
| **生命系统**     | 初始100HP，受怪兽定时攻击扣除                                               |
| 解锁核心         | 推动剧情解锁猫咪伙伴                                                        |

#### **2. 猫咪伙伴**
| **角色**         | **专精题型**       | **核心能力**                                                     |
|------------------|-------------------|------------------------------------------------------------------|
| 音律喵·Melody    | 听音选类          | 音素辨识力，快速匹配听到的音与对应字母或组合                        |
| 真理喵·Justice   | 判断类            | 快速判别音素与单词的正确性，并按规则分类                           |
| 法师喵·Phona     | 看题选发音类      | 视觉-听觉双通道匹配，可瞬间读出正确发音                           |
| 剑客喵·Syllabo   | 分割类            | 掌握音节划分与音素切分技巧，将复杂单词分解成简单音节                |
| 符文喵·Ipa       | 音标类            | 精通音标与发音的对应关系，可快速解码发音或反查符号                  |

| **被动特性**     | ①无独立血量 ②切换时保留当前题型进度（不重置连击）                             |

---

### **二、战斗系统**  
#### **1. 核心规则**  
```mermaid
graph LR
A[怪兽定时攻击] --> B[主角扣血]
C[答题错误] --> D[连击清零]
E[答题正确] --> F[普攻伤害+连击+1]
G[连击达标] --> H[玩家可以选择想要释放技能]
```

#### **2. 关键机制**  
| **机制**          | **规则细节**                                                                |
|-------------------|-----------------------------------------------------------------------------|
| **怪兽攻击**      | 固定间隔20秒攻击1次（可被技能冻结/打断）                                     |
| **答题反馈**      | 错误：连击清零+无扣血惩罚<br>正确：基础伤害+连击点累积                       |
| **技能触发**      | 3连击=初级技能｜5连击=中级技能｜7连击=高级技能（释放后连击槽保留1点）         |
| **题型切换**      | 5大题型分类：<br>①听音选类（4种）②判断类（4种）③看题选发音类（4种）④分割类（1种）⑤音标类（2种） |
| **猫咪切换**      | 即时生效：<br>①刷新为当前猫题型 ②继承连击进度 ③不触发攻击间隔重置            |

---

### **三、关卡系统**  
#### **1. 章节设计**  
| **章节**       | **解锁内容**       | **怪兽特性**                | **特殊机制**                  |
|----------------|--------------------|---------------------------|-----------------------------|
| 辅音浅滩       | 真理喵·Justice     | 攻击间隔25秒               | 答题正确延迟下次攻击5秒        |
| 元音丛林       | 法师喵·Phona       | 攻击附带减速（间隔-3秒）    | 连续3次正确重置攻击计时        |
| 音节峡谷       | 剑客喵·Syllabo     | 免疫初级技能               | 切换猫咪冻结攻击计时3秒        |
| 符文圣殿       | 符文喵·Ipa         | 攻击间隔15秒               | 高级题型解锁，连击要求+2       |

#### **2. BOSS战规则**  
- **三阶段攻击进化**：  
  > 阶段1：间隔20秒 → 阶段2：间隔15秒 → 阶段3：间隔10秒  
- **破防机制**：仅当连击≥5时造成的伤害有效  

---

### **四、成长与经济系统**  
#### **1. 主角成长**  
| **成长线**      | **效果**                                                                  |
|-----------------|--------------------------------------------------------------------------|
| 等级提升        | 每级+10HP上限，解锁新章节                                                |
| 爱心收集        | 每章节隐藏3颗爱心，集齐永久+5%HP                                         |
| 技能强化        | 消耗技能水晶升级猫咪技能范围/伤害                                        |

#### **2. 经济体系**  
| **资源**        | **用途**                  | **获取方式**                              |
|-----------------|--------------------------|-----------------------------------------|
| 时间沙漏        | 暂停怪兽计时10秒          | 连击达成奖励/商城购买                     |
| 技能水晶        | 解锁猫咪高阶技能节点      | BOSS战首通/成就系统                       |
| 金币            | 购买主角/猫咪外观         | 每日任务/答题连击奖励                     |

---

### **五、辅助系统**  
#### **1. 动态难度调节**  
| **情景**               | **系统响应**                                                              |
|------------------------|--------------------------------------------------------------------------|
| 主角HP<30%             | 怪兽攻击间隔+5秒                                                         |
| 连续错误5次            | 强制弹出规则提示（可关闭）                                                |
| 家长设置-学习模式       | 关闭攻击计时/错误不打断连击                                               |

#### **2. 教学机制**  
| **功能**           | **触发条件**            | **效果**                                  |
|--------------------|------------------------|------------------------------------------|
| 规则可视化         | 每题首次错误            | 动态拆解单词（如c-a-t）并高亮目标音素      |
| 攻击预警           | 怪兽攻击前5秒           | 屏幕边缘红光闪烁+音效提示                 |
| 濒死保护           | HP≤10%                 | 自动生成临时护盾（抵挡1次攻击）            |

---

### **六、剧情系统**  
#### **1. 解锁逻辑**  
```plaintext
主线剧情：
[音素混乱] → 解锁真理喵·Justice → [发音迷雾] → 解锁法师喵·Phona → [音节分裂] → 解锁剑客喵·Syllabo → [符文觉醒] → 解锁符文喵·Ipa
```
#### **2. 角色互动**  
- 解锁新猫咪时触发**技能教学剧场**（30秒可跳过动画）  
- 主角濒死时猫咪触发**守护台词**（“让我来帮你！”）  

---

**设计核心聚焦**：  
1. **压力可控性**：定时攻击替代错误惩罚，连击机制提供反制手段  
2. **策略自由度**：切换猫咪无代价+连击继承保障题型实验空间  
3. **生存驱动学习**：攻击计时器天然激励快速准确答题  
4. **成长可视化**：爱心收集直接强化生存能力，形成正循环  
