

### 听音选
* 听音，选字母
    * 随难度提升，扩展考核范围、选项数增加、变成多选等
* 听音，选字母组合
    * 随难度提升，扩展考核范围、选项数增加、变成多选等
* 听完整单词，选字母或字母组合补全单词
    * 随难度提升，扩展考核范围、选项数增加、单词长度等
* 听完整单词，选字母或字母组合拼成单词
    * 随难度提升，扩展考核范围、选项数增加、单词长度等

### 判断
* 听音，判断提供的答案是否正确
    * 随难度提升，扩展考核范围（字母、字母组合、单词...）
* 听音，分类单词
    * 随难度提升，扩展考核范围、选项数增加、出现干扰项等
* 听短语，从短语中找出带有题目要求发音的词语

* 听句子，从句子中找出带有题目要求发音的词语

### 看题选发音
* 看字母，选发音

* 看字母组合，选发音

* 看单词划线部分，选发音

* 看单词，选完整单词发音

### 分割
* 听音，分割单词，按照发音切分单词中的字母

### 听音选音标（进阶版）

### 根据音标选发音（进阶版）





基于上面的建议，帮我生成一份完整的需求文档，专注于需求方面，不用设计技术或架构等，基于以下要求，并进行丰满：
* 主角是各种猫咪，敌人是各种怪兽
* 战斗过程中，可以切换猫咪，从而切换题型
* 不同猫咪掌握不同的技能，对应不同的题型，玩家通过不停地答题进行普通攻击
* 如果能够连续答对，则可以触发技能
* 技能有初级中级高级...，通过升级角色可以获得更高等级的技能；不过，升级后的猫咪对应的题型难度将提高



